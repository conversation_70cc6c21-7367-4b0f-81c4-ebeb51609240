import { Section } from "@/types/blocks/section";

/**
 * Prompt Showcase 组件的数据项接口
 */
export interface PromptShowcaseItem {
  /** 标题 */
  title: string;
  /** 描述 */
  description?: string;
  /** Prompt 内容 */
  prompt: string;
  /** 主要展示图片 */
  image?: {
    src: string;
    alt?: string;
  };
  /** 分类标签 */
  category?: string;
  /** 标签列表 */
  tags?: string[];
}

/**
 * Comparison Showcase 组件的数据项接口
 */
export interface ComparisonShowcaseItem {
  /** 标题 */
  title: string;
  /** 描述 */
  description?: string;
  /** Prompt 内容 */
  prompt: string;
  /** 处理前图片 */
  beforeImage: {
    src: string;
    alt: string;
  };
  /** 处理后图片 */
  afterImage: {
    src: string;
    alt: string;
  };
  /** 分类标签 */
  category?: string;
  /** 标签列表 */
  tags?: string[];
}

/**
 * Prompt Showcase 组件的 Props 接口
 */
export interface PromptShowcaseProps {
  section: Section & {
    items?: PromptShowcaseItem[];
  };
}

/**
 * Comparison Showcase 组件的 Props 接口
 */
export interface ComparisonShowcaseProps {
  section: Section & {
    items?: ComparisonShowcaseItem[];
  };
}
